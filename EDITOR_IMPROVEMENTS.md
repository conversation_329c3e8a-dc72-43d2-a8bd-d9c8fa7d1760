# C<PERSON><PERSON> thiện Editor - Hỗ trợ Copy từ Word

## Tổng quan

Dự án đã được cải thiện để hỗ trợ tốt hơn việc copy nội dung từ Microsoft Word và giữ nguyên định dạng. <PERSON><PERSON> hai lựa chọn editor:

1. **Summernote (Đã cải thiện)** - Editor hiện tại với khả năng xử lý Word được nâng cấp
2. **TinyMCE (Mới)** - Editor hiện đại với khả năng xử lý Word tốt hơn

## Các cải thiện đã thực hiện

### 1. Summernote Enhancements

#### Files đã thay đổi:
- `resources/views/admin/init.blade.php` - Cấu hình Summernote nâng cao
- `resources/views/instructor/init.blade.php` - Cấu hình Summernote cho instructor
- `public/assets/global/summernote/summernote-word-paste.js` - Plugin xử lý paste từ Word

#### Tính năng mới:
- **Enhanced Paste Handling**: Xử lý paste từ Word với thuật toán làm sạch nâng cao
- **Word Content Cleaning**: Loại bỏ các thẻ và style không cần thiết từ Word
- **Notification System**: Thông báo khi paste thành công
- **Improved Toolbar**: Thêm nhiều công cụ formatting
- **Better Font Support**: Hỗ trợ nhiều font chữ hơn

#### Cấu hình mới:
```javascript
// Enhanced paste handling
callbacks: {
    onPaste: function (e) {
        var clipboardData = e.originalEvent.clipboardData || window.clipboardData;
        var pastedData = clipboardData.getData('text/html') || clipboardData.getData('text/plain');
        
        if (pastedData) {
            e.preventDefault();
            
            // Clean up Word-specific formatting while preserving structure
            var cleanedData = pastedData
                .replace(/<!--[\s\S]*?-->/g, '') // Remove comments
                .replace(/<o:p\s*\/?>|<\/o:p>/gi, '') // Remove Word paragraph tags
                // ... more cleaning rules
            
            $(this).summernote('pasteHTML', cleanedData);
        }
    }
}
```

### 2. TinyMCE Integration

#### Files mới:
- `public/assets/global/tinymce/tinymce-config.js` - Cấu hình TinyMCE
- `app/Http/Controllers/Admin/EditorController.php` - Controller xử lý upload và cleaning
- `resources/views/admin/test-editor.blade.php` - Trang test so sánh editors

#### Tính năng TinyMCE:
- **PowerPaste Plugin**: Xử lý paste từ Word chuyên nghiệp
- **Advanced Toolbar**: Toolbar đầy đủ tính năng
- **Image Upload**: Hỗ trợ upload ảnh drag & drop
- **Template System**: Hệ thống template có sẵn
- **Auto-save**: Tự động lưu nội dung
- **Word Cleaning Button**: Nút dọn dẹp định dạng Word thủ công

### 3. Enhanced Styling

#### File CSS mới:
- `public/assets/global/css/enhanced-editor.css` - Styles cải thiện cho cả hai editor

#### Cải thiện giao diện:
- **Modern Design**: Thiết kế hiện đại với border-radius và shadows
- **Better Hover Effects**: Hiệu ứng hover mượt mà
- **Dark Mode Support**: Hỗ trợ chế độ tối
- **Mobile Responsive**: Tối ưu cho mobile
- **Loading States**: Trạng thái loading và success/error

### 4. API Endpoints

#### Routes mới trong `routes/admin.php`:
```php
// Editor Routes
Route::controller(\App\Http\Controllers\Admin\EditorController::class)->group(function () {
    Route::post('upload-image', 'uploadImage')->name('editor.upload.image');
    Route::post('upload-image-summernote', 'uploadImageSummernote')->name('editor.upload.image.summernote');
    Route::post('clean-word-content', 'cleanWordContent')->name('editor.clean.word');
});

// Test Editor Route
Route::get('test-editor', function () {
    return view('admin.test-editor');
})->name('test.editor');
```

## Cách sử dụng

### 1. Test Editors

Truy cập: `/admin/test-editor` để so sánh hai editor:

1. **Summernote (Cải thiện)**: Editor hiện tại với khả năng xử lý Word được nâng cấp
2. **TinyMCE (Mới)**: Editor hiện đại với PowerPaste plugin

### 2. Test với Word Content

1. Mở Microsoft Word
2. Tạo document với định dạng phức tạp (màu sắc, font, danh sách, bảng)
3. Copy nội dung (Ctrl+C)
4. Paste vào editor (Ctrl+V)
5. So sánh kết quả

### 3. Sử dụng trong Production

#### Để sử dụng Summernote cải thiện:
- Không cần thay đổi gì, tất cả editor hiện tại đã được cải thiện tự động

#### Để chuyển sang TinyMCE:
1. Thay class `text_editor` thành `tinymce-editor`
2. Include TinyMCE script:
```html
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js"></script>
<script src="{{ asset('assets/global/tinymce/tinymce-config.js') }}"></script>
```

## Word Paste Cleaning Features

### Các thẻ và thuộc tính được loại bỏ:
- Word comments (`<!--...-->`)
- Word-specific namespaces (`<o:p>`, `<w:*>`)
- MSO styles (`mso-*`)
- Empty attributes (`style=""`, `class=""`)
- Word-specific classes (`MsoNormal`, etc.)
- Excessive margins and spacing
- Font-family và font-size (để editor tự xử lý)

### Các định dạng được giữ lại:
- Text formatting (bold, italic, underline)
- Colors (foreground và background)
- Text alignment
- Lists (ordered và unordered)
- Tables
- Links
- Basic structure (headings, paragraphs)

## Troubleshooting

### Nếu Summernote không hoạt động:
1. Kiểm tra console browser có lỗi JavaScript không
2. Đảm bảo file `summernote-word-paste.js` được load
3. Xóa cache browser

### Nếu TinyMCE không load:
1. Kiểm tra kết nối internet (TinyMCE từ CDN)
2. Kiểm tra file `tinymce-config.js` có tồn tại không
3. Đảm bảo không có conflict với jQuery

### Upload ảnh không hoạt động:
1. Kiểm tra quyền ghi thư mục `storage/app/public/uploads/editor`
2. Chạy `php artisan storage:link` nếu chưa có
3. Kiểm tra CSRF token

## Performance Notes

- **Summernote**: Nhẹ hơn, load nhanh hơn
- **TinyMCE**: Nặng hơn nhưng tính năng mạnh hơn
- **Word Cleaning**: Xử lý real-time, không ảnh hưởng performance đáng kể

## Browser Support

- **Chrome**: Full support
- **Firefox**: Full support  
- **Safari**: Full support
- **Edge**: Full support
- **IE11**: Limited support (chỉ Summernote)

## Recommendations

### Cho blog/content đơn giản:
- Sử dụng **Summernote cải thiện** (đã có sẵn)

### Cho content phức tạp/professional:
- Chuyển sang **TinyMCE** để có trải nghiệm tốt nhất

### Cho mobile users:
- Cả hai đều responsive, nhưng Summernote nhẹ hơn cho mobile

## Future Improvements

1. **Offline TinyMCE**: Download và host TinyMCE locally
2. **Custom Plugins**: Tạo plugin riêng cho các tính năng đặc biệt
3. **Advanced Templates**: Thêm nhiều template có sẵn
4. **Collaborative Editing**: Hỗ trợ chỉnh sửa đồng thời
5. **Version History**: Lưu lịch sử thay đổi
