<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class EditorController extends Controller
{
    /**
     * Upload image for TinyMCE editor
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage(Request $request)
    {
        try {
            // Validate the uploaded file
            $request->validate([
                'file' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048'
            ]);

            $file = $request->file('file');
            
            // Generate unique filename
            $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            
            // Store the file in public/uploads/editor directory
            $path = $file->storeAs('uploads/editor', $filename, 'public');
            
            // Return the URL for TinyMCE
            $url = asset('storage/' . $path);
            
            return response()->json([
                'location' => $url,
                'success' => true,
                'message' => 'Image uploaded successfully'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Upload failed: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Upload image for Summernote editor
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImageSummernote(Request $request)
    {
        try {
            // Validate the uploaded file
            $request->validate([
                'file' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048'
            ]);

            $file = $request->file('file');
            
            // Generate unique filename
            $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            
            // Store the file in public/uploads/editor directory
            $path = $file->storeAs('uploads/editor', $filename, 'public');
            
            // Return the URL for Summernote
            $url = asset('storage/' . $path);
            
            return response()->json([
                'url' => $url,
                'success' => true,
                'message' => 'Image uploaded successfully'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Upload failed: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Clean Word content (API endpoint)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cleanWordContent(Request $request)
    {
        try {
            $content = $request->input('content', '');
            
            if (empty($content)) {
                return response()->json([
                    'error' => 'No content provided',
                    'success' => false
                ], 400);
            }
            
            $cleanedContent = $this->cleanWordHtml($content);
            
            return response()->json([
                'content' => $cleanedContent,
                'success' => true,
                'message' => 'Content cleaned successfully'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Cleaning failed: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Clean Word HTML content
     *
     * @param string $html
     * @return string
     */
    private function cleanWordHtml($html)
    {
        if (empty($html)) {
            return '';
        }
        
        // Remove Word-specific comments and conditional statements
        $html = preg_replace('/<!--[\s\S]*?-->/', '', $html);
        $html = preg_replace('/<!\[if[\s\S]*?\]>/i', '', $html);
        $html = preg_replace('/<!\[endif\]>/i', '', $html);
        
        // Remove Word-specific namespaces and tags
        $html = preg_replace('/<\/?\w+:[^>]*>/i', '', $html);
        $html = preg_replace('/<\?xml[^>]*>/i', '', $html);
        
        // Clean up MSO styles and attributes
        $html = preg_replace('/\s*mso-[^:]+:[^;"\']*;?/i', '', $html);
        $html = preg_replace('/\s*MARGIN[^;"\']*;?/i', '', $html);
        $html = preg_replace('/\s*margin[^;"\']*;?/i', '', $html);
        $html = preg_replace('/\s*tab-stops[^;"\']*;?/i', '', $html);
        $html = preg_replace('/\s*text-indent[^;"\']*;?/i', '', $html);
        $html = preg_replace('/\s*line-height[^;"\']*;?/i', '', $html);
        
        // Clean font and size attributes
        $html = preg_replace('/\s*font-family[^;"\']*;?/i', '', $html);
        $html = preg_replace('/\s*font-size[^;"\']*;?/i', '', $html);
        $html = preg_replace('/\s*FONT-FAMILY[^;"\']*;?/i', '', $html);
        $html = preg_replace('/\s*FONT-SIZE[^;"\']*;?/i', '', $html);
        
        // Remove Word-specific class names
        $html = preg_replace('/\s*class="[^"]*Mso[^"]*"/i', '', $html);
        $html = preg_replace('/\s*class="[^"]*Word[^"]*"/i', '', $html);
        $html = preg_replace('/\s*class="[^"]*Normal[^"]*"/i', '', $html);
        
        // Clean up empty attributes
        $html = preg_replace('/\s*style="\s*"/i', '', $html);
        $html = preg_replace('/\s*class="\s*"/i', '', $html);
        $html = preg_replace('/\s*lang="[^"]*"/i', '', $html);
        $html = preg_replace('/\s*xml:lang="[^"]*"/i', '', $html);
        
        // Remove empty tags
        $html = preg_replace('/<span[^>]*>\s*<\/span>/i', '', $html);
        $html = preg_replace('/<div[^>]*>\s*<\/div>/i', '', $html);
        $html = preg_replace('/<p[^>]*>\s*<\/p>/i', '', $html);
        $html = preg_replace('/<font[^>]*>\s*<\/font>/i', '', $html);
        
        // Convert Word-specific elements
        $html = preg_replace('/<b\s*>/i', '<strong>', $html);
        $html = preg_replace('/<\/b>/i', '</strong>', $html);
        $html = preg_replace('/<i\s*>/i', '<em>', $html);
        $html = preg_replace('/<\/i>/i', '</em>', $html);
        
        // Clean up whitespace and special characters
        $html = str_replace('&nbsp;', ' ', $html);
        $html = str_replace("\u{00A0}", ' ', $html); // Unicode non-breaking space
        $html = preg_replace('/\s+/', ' ', $html);
        $html = preg_replace('/>\s+</', '><', $html);
        
        return trim($html);
    }
}
