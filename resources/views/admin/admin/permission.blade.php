@extends('layouts.admin')
@push('title', '<PERSON><PERSON> quyền')
@push('meta')@endpush
@push('css')
<style>
    .permission-group {
        margin-bottom: 20px;
        border: 1px solid #eee;
        border-radius: 8px;
    }
    .permission-group-header {
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        border-radius: 8px 8px 0 0;
    }
    .permission-group-header h5 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
    }
    .permission-group-body {
        padding: 0;
    }
    .permission-item {
        padding: 8px 15px;
        border-bottom: 1px solid #f1f1f1;
    }
    .permission-item:last-child {
        border-bottom: none;
    }
    .select-all-checkbox {
        margin-left: 10px;
    }
    .checkmark {
        display: inline-block;
        width: 18px;
        height: 18px;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 3px;
        position: relative;
    }
    .checkmark.checked {
        background-color: #4e73df;
        border-color: #4e73df;
    }
    .checkmark.checked:after {
        content: '';
        position: absolute;
        left: 6px;
        top: 2px;
        width: 5px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }
    .toggle-icon {
        float: right;
    }
</style>
@endpush
@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    {{ get_phrase('Admin Permissions') }}
                </h4>

                <a href="{{ route('admin.admins.index') }}" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                    <span class="fi-rr-arrow-alt-left"></span>
                    <span>{{ get_phrase('Back') }}</span>
                </a>
            </div>
        </div>
    </div>
    @php
        // Define permissions with their corresponding route names
        $permissionGroups = [
            'Dashboard' => [
                'admin.dashboard' => get_phrase('Dashboard'),
            ],
            'Categories' => [
                'admin.categories' => get_phrase('Category'),
                'admin.category.create' => get_phrase('Create Category'),
                'admin.category.edit' => get_phrase('Edit Category'),
                'admin.category.delete' => get_phrase('Delete Category'),
            ],
            'Courses' => [
                'admin.courses' => get_phrase('Courses'),
                'admin.course.create' => get_phrase('Create Course'),
                'admin.course.edit' => get_phrase('Edit Course'),
                'admin.course.update' => get_phrase('Update Course'),
                'admin.course.duplicate' => get_phrase('Duplicate Course'),
                'admin.course.delete' => get_phrase('Delete Course'),
                'admin.course.status' => get_phrase('Course Status'),
                'admin.course.draft' => get_phrase('Course Draft'),
                'admin.course.approval' => get_phrase('Course Approval'),
            ],
            'Curriculum' => [
                'admin.section.store' => get_phrase('Create Section'),
                'admin.section.update' => get_phrase('Update Section'),
                'admin.section.delete' => get_phrase('Delete Section'),
                'admin.lesson.store' => get_phrase('Create Lesson'),
                'admin.lesson.edit' => get_phrase('Edit Lesson'),
                'admin.lesson.delete' => get_phrase('Delete Lesson'),
                'admin.section.sort' => get_phrase('Sort Sections'),
                'admin.lesson.sort' => get_phrase('Sort Lessons'),
            ],
            'Quiz & Questions' => [
                'admin.course.quiz.store' => get_phrase('Create Quiz'),
                'admin.course.quiz.update' => get_phrase('Update Quiz'),
                'admin.course.quiz.delete' => get_phrase('Delete Quiz'),
                'admin.quiz.participant.result' => get_phrase('Quiz Results'),
                'admin.course.question.store' => get_phrase('Create Question'),
                'admin.course.question.update' => get_phrase('Update Question'),
                'admin.course.question.delete' => get_phrase('Delete Question'),
                'admin.quiz.result.preview' => get_phrase('Preview Quiz Result'),
                'admin.course.question.sort' => get_phrase('Sort Questions'),
                'admin.load.question.type' => get_phrase('Load Question Type'),
            ],
            'Live Classes' => [
                'admin.live.class.store' => get_phrase('Create Live Class'),
                'admin.live.class.update' => get_phrase('Update Live Class'),
                'admin.live.class.delete' => get_phrase('Delete Live Class'),
                'admin.live.class.start' => get_phrase('Start Live Class'),
                'admin.live.class.settings' => get_phrase('Live Class Settings'),
            ],
            'Bootcamp' => [
                'admin.bootcamps' => get_phrase('Bootcamp'),
                'admin.bootcamp.create' => get_phrase('Create Bootcamp'),
                'admin.bootcamp.edit' => get_phrase('Edit Bootcamp'),
                'admin.bootcamp.delete' => get_phrase('Delete Bootcamp'),
                'admin.bootcamp.status' => get_phrase('Bootcamp Status'),
                'admin.bootcamp.duplicate' => get_phrase('Duplicate Bootcamp'),
                'admin.bootcamp.purchase.history' => get_phrase('Bootcamp Purchase History'),
                'admin.bootcamp.module.store' => get_phrase('Create Bootcamp Module'),
                'admin.bootcamp.module.update' => get_phrase('Update Bootcamp Module'),
                'admin.bootcamp.module.delete' => get_phrase('Delete Bootcamp Module'),
                'admin.bootcamp.categories' => get_phrase('Bootcamp Categories'),
                'admin.bootcamp.module.sort' => get_phrase('Sort Bootcamp Modules'),
                'admin.bootcamp.purchase.invoice' => get_phrase('Bootcamp Purchase Invoice'),
                'admin.bootcamp.live.class.join' => get_phrase('Join Bootcamp Live Class'),
                'admin.bootcamp.class.end' => get_phrase('End Bootcamp Class'),
                'admin.update.on.end.class' => get_phrase('Update On End Class'),
                'admin.bootcamp.resource.download' => get_phrase('Download Bootcamp Resource'),
            ],
            'Team Training' => [
                'admin.team.packages' => get_phrase('Team Training'),
                'admin.team.packages.create' => get_phrase('Create Team Package'),
                'admin.team.packages.edit' => get_phrase('Edit Team Package'),
                'admin.team.packages.delete' => get_phrase('Delete Team Package'),
                'admin.team.packages.duplicate' => get_phrase('Duplicate Team Package'),
                'admin.team.toggle.status' => get_phrase('Toggle Team Package Status'),
                'admin.team.packages.purchase.history' => get_phrase('Team Packages Purchase History'),
                'admin.get.courses.by.privacy' => get_phrase('Get Courses By Privacy'),
                'admin.get.course.price' => get_phrase('Get Course Price'),
                'admin.team.packages.purchase.invoice' => get_phrase('Team Package Purchase Invoice'),
            ],
            'Tutor Booking' => [
                'admin.tutor_subjects' => get_phrase('Tutor Subjects'),
                'admin.tutor_subject_create' => get_phrase('Create Tutor Subject'),
                'admin.tutor_subject_edit' => get_phrase('Edit Tutor Subject'),
                'admin.tutor_subject_delete' => get_phrase('Delete Tutor Subject'),
                'admin.tutor_subject_status' => get_phrase('Tutor Subject Status'),
                'admin.tutor_categories' => get_phrase('Tutor Categories'),
                'admin.tutor_category_create' => get_phrase('Create Tutor Category'),
                'admin.tutor_category_edit' => get_phrase('Edit Tutor Category'),
                'admin.tutor_category_delete' => get_phrase('Delete Tutor Category'),
                'admin.tutor_category_status' => get_phrase('Tutor Category Status'),
            ],
            'Students' => [
                'admin.student.index' => get_phrase('Student'),
                'admin.student.create' => get_phrase('Create Student'),
                'admin.student.edit' => get_phrase('Edit Student'),
                'admin.student.delete' => get_phrase('Delete Student'),
                'admin.student.status' => get_phrase('Student Status'),
                'admin.student.enrollments' => get_phrase('View Student Enrollments'),
                'admin.student.enroll' => get_phrase('Enrollment'),
                'admin.enroll.history' => get_phrase('Enroll History'),
                'admin.enroll.history.delete' => get_phrase('Delete Enroll History'),
                'admin.student.get' => get_phrase('Get Students'),
                'admin.student.devices' => get_phrase('Manage Student Devices'),
                'admin.student.device.delete' => get_phrase('Delete Student Device'),
                'admin.student.reset.violations' => get_phrase('Reset Login Violations'),
            ],
            'Instructors' => [
                'admin.instructor.index' => get_phrase('Instructor'),
                'admin.instructor.create' => get_phrase('Create Instructor'),
                'admin.instructor.edit' => get_phrase('Edit Instructor'),
                'admin.instructor.delete' => get_phrase('Delete Instructor'),
                'admin.instructor.course' => get_phrase('Instructor Courses'),
                'admin.instructor.payout' => get_phrase('Instructor Payout'),
                'admin.instructor.payment' => get_phrase('Instructor Payment'),
                'admin.instructor.setting' => get_phrase('Instructor Settings'),
                'admin.instructor.application' => get_phrase('Instructor Applications'),
                'admin.instructor.application.approve' => get_phrase('Approve Instructor Application'),
                'admin.instructor.application.delete' => get_phrase('Delete Instructor Application'),
                'admin.instructor.revenue' => get_phrase('Instructor Revenue'),
                'admin.instructor.payout.filter' => get_phrase('Filter Instructor Payout'),
                'admin.instructor.payout.invoice' => get_phrase('Instructor Payout Invoice'),
                'admin.instructor.application.download' => get_phrase('Download Instructor Application'),
                'admin.instructor_revenue.delete' => get_phrase('Delete Instructor Revenue'),
            ],
            'Administrators' => [
                'admin.admins.index' => get_phrase('Admin'),
                'admin.admins.create' => get_phrase('Create Admin'),
                'admin.admins.edit' => get_phrase('Edit Admin'),
                'admin.admins.delete' => get_phrase('Delete Admin'),
                'admin.admins.permission' => get_phrase('Admin Permissions'),
                'admin.manage.profile' => get_phrase('Manage Profile'),
            ],
            'Payments & Transactions' => [
                'admin.offline.payments' => get_phrase('Offline Payment'),
                'admin.offline.payment.doc' => get_phrase('Offline Payment Document'),
                'admin.offline.payment.accept' => get_phrase('Accept Offline Payment'),
                'admin.offline.payment.decline' => get_phrase('Decline Offline Payment'),
                'admin.offline.payment.delete' => get_phrase('Delete Offline Payment'),
                'admin.revenue' => get_phrase('Admin Revenue'),
                'admin.revenue.delete' => get_phrase('Delete Revenue'),
                'admin.purchase.history' => get_phrase('Purchase history'),
                'admin.invoice' => get_phrase('Invoice'),
                'admin.purchase.history.invoice' => get_phrase('Purchase History Invoice'),
            ],
            'Marketing' => [
                'admin.newsletter' => get_phrase('Newsletter'),
                'admin.subscribed_user' => get_phrase('Newsletter Subscriber'),
                'admin.newsletters.form' => get_phrase('Newsletter Form'),
                'admin.newsletter.delete' => get_phrase('Delete Newsletter'),
                'admin.subscribed_user.delete' => get_phrase('Delete Subscribed User'),
                'admin.get.user' => get_phrase('Get Users for Newsletter'),
                'admin.coupons' => get_phrase('Coupon'),
                'admin.coupon.create' => get_phrase('Create Coupon'),
                'admin.coupon.edit' => get_phrase('Edit Coupon'),
                'admin.coupon.delete' => get_phrase('Delete Coupon'),
                'admin.coupon.status' => get_phrase('Coupon Status'),
                'admin.marketing.popup' => get_phrase('Marketing Popup'),
                'admin.marketing.popup.store' => get_phrase('Create Marketing Popup'),
                'admin.marketing.popup.update' => get_phrase('Update Marketing Popup'),
                'admin.marketing.popup.delete' => get_phrase('Delete Marketing Popup'),
            ],
            'Affiliates' => [
                'admin.affiliate' => get_phrase('Affiliate'),
                'admin.affiliate.withdraws' => get_phrase('Affiliate Withdraws'),
                'admin.affiliate.settings' => get_phrase('Affiliate Settings'),
                'admin.affiliate.status' => get_phrase('Update Affiliate Status'),
                'admin.affiliate.withdraw.status' => get_phrase('Update Withdraw Status'),
                'admin.affiliate.setting.store' => get_phrase('Store Affiliate Settings'),
            ],
            'Communication' => [
                'admin.message' => get_phrase('Message'),
                'admin.contacts' => get_phrase('Contact User'),
                'admin.contact.delete' => get_phrase('Delete Contact'),
            ],
            'Blogging' => [
                'admin.blogs' => get_phrase('Blog'),
                'admin.blog.create' => get_phrase('Create Blog'),
                'admin.blog.edit' => get_phrase('Edit Blog'),
                'admin.blog.delete' => get_phrase('Delete Blog'),
                'admin.blog.status' => get_phrase('Blog Status'),
                'admin.blog.pending' => get_phrase('Pending Blog List'),
                'admin.blog.category' => get_phrase('Blog Category'),
                'admin.blog.settings' => get_phrase('Blog Settings'),
                'admin.blog.category.create' => get_phrase('Create Blog Category'),
                'admin.blog.category.delete' => get_phrase('Delete Blog Category'),
            ],
            'Reviews' => [
                'admin.review.create' => get_phrase('User Reviews'),
                'admin.review.edit' => get_phrase('Edit Review'),
                'admin.review.delete' => get_phrase('Delete Review'),
            ],
            'Page Builder' => [
                'admin.pages' => get_phrase('Pages'),
                'admin.page.create' => get_phrase('Create Page'),
                'admin.page.edit' => get_phrase('Edit Page'),
                'admin.page.delete' => get_phrase('Delete Page'),
                'admin.page.status' => get_phrase('Page Status'),
                'admin.page.layout.edit' => get_phrase('Edit Page Layout'),
                'admin.page.all.builder.developer.file' => get_phrase('Get Builder Developer File'),
                'admin.page.layout.update' => get_phrase('Update Page Layout'),
                'admin.page.layout.image.update' => get_phrase('Update Page Layout Image'),
                'admin.page.preview' => get_phrase('Preview Page'),
            ],
            'Settings' => [
                'admin.system.settings' => get_phrase('System Settings'),
                'admin.website.settings' => get_phrase('Website Settings'),
                'admin.payment.settings' => get_phrase('Payment Settings'),
                'admin.manage.language' => get_phrase('Language Settings'),
                'admin.certificate.settings' => get_phrase('Certificate'),
                'admin.certificate.builder' => get_phrase('Certificate Builder'),
                'admin.open.ai.settings' => get_phrase('Open AI Settings'),
                'admin.seo.settings' => get_phrase('SEO Settings'),
                'admin.drip.settings' => get_phrase('Drip Content Settings'),
                'admin.notification.settings' => get_phrase('Notification Settings'),
                'admin.player.settings' => get_phrase('Player Settings'),
                'admin.embed.settings' => 'Nhúng form đăng ký',
                'admin.api.configurations' => get_phrase('API Configurations'),
                'admin.language.phrase.edit' => get_phrase('Edit Language Phrase'),
                'admin.language.phrase.import' => get_phrase('Import Language Phrase'),
                'admin.language.delete' => get_phrase('Delete Language'),
                'admin.api.configuration.update' => get_phrase('Update API Configuration'),
                'admin.open.ai.generate' => get_phrase('Generate Open AI Content'),
            ],
            'Others' => [
                'admin.about' => get_phrase('About'),
                'admin.bunny.upload' => get_phrase('Video Upload Bunny'),
                'admin.addons' => get_phrase('Addons'),
                'admin.bunny.statistics' => get_phrase('Bunny Statistics'),
                'admin.bunny.list' => get_phrase('Bunny Video List'),
                'admin.save_valid_purchase_code' => get_phrase('Save Valid Purchase Code'),
                'admin.select.language' => get_phrase('Select Language'),
                'admin.addons.toggle' => get_phrase('Toggle Addon Status'),
                'admin.addons.seed' => get_phrase('Seed Addons'),
            ],
        ];
        $permission_row = DB::table('permissions')
            ->where('admin_id', $admin->id)
            ->first();
        $permissions = json_decode($permission_row->permissions ?? '{}', true);
    @endphp

    <div class="row">
        <div class="col-xl-8">
            <div class="ol-card p-4">
                <div class="ol-card-body">
                    <div class="col-12 mb-4">
                        <p class="column-title mb-1">{{ get_phrase('Assign permission for') }}: {{ $admin->name }}</p>
                        <small class="text-muted">
                            <strong>{{ get_phrase('Note') }}</strong> :
                            {{ get_phrase('You can toggle the switch for enabling or disabling a feature to access') }}</small>
                    </div>

                    <div class="accordion" id="permissionAccordion">
                        @foreach($permissionGroups as $groupName => $groupPermissions)
                            <div class="permission-group mb-3">
                                <div class="permission-group-header d-flex justify-content-between align-items-center collapsed"
                                     data-bs-toggle="collapse"
                                     data-bs-target="#group{{ str_replace(' ', '', $groupName) }}"
                                     aria-expanded="false"
                                     aria-controls="group{{ str_replace(' ', '', $groupName) }}">
                                    <h5>{{ get_phrase($groupName) }}</h5>
                                    <div>
                                        <span class="select-all-checkbox">
                                            <input type="checkbox"
                                                   class="group-select-all"
                                                   id="selectAll{{ str_replace(' ', '', $groupName) }}"
                                                   data-group="{{ str_replace(' ', '', $groupName) }}"
                                                   @if(is_array($permissions) && count(array_intersect(array_keys($groupPermissions), $permissions)) == count($groupPermissions)) checked @endif
                                                   >
                                            <label for="selectAll{{ str_replace(' ', '', $groupName) }}">{{ get_phrase('Select All') }}</label>
                                        </span>
                                        <i class="toggle-icon fas fa-chevron-down"></i>
                                    </div>
                                </div>
                                <div id="group{{ str_replace(' ', '', $groupName) }}" class="collapse" data-parent="#permissionAccordion">
                                    <div class="permission-group-body">
                                        @foreach($groupPermissions as $route => $title)
                                            <div class="permission-item d-flex justify-content-between align-items-center">
                                                <span>{{ $title }}</span>
                                                <div class="form-check form-switch">
                                                    <input type="checkbox"
                                                           class="form-check-input permission-checkbox"
                                                           id="{{ $admin->id . '-' . $route }}"
                                                           data-group="{{ str_replace(' ', '', $groupName) }}"
                                                           data-switch="bool"
                                                           onchange="setPermission('{{ $admin->id }}', '{{ $route }}')"
                                                           @if (is_array($permissions) && in_array($route, $permissions)) checked @endif>
                                                    <label for="{{ $admin->id . '-' . $route }}" data-on-label="On" data-off-label="Off"></label>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div> <!-- end card body-->
            </div> <!-- end card -->
        </div><!-- end col-->
    </div>
@endsection
@push('js')
    <script>
        "use strict";

        function setPermission(user_id, permission) {
            $.ajax({
                type: "post",
                url: "{{ route('admin.admins.permission.store') }}/" + user_id,
                data: {
                    user_id: user_id,
                    permission: permission,
                },
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response == 1) {
                        success("{{ get_phrase('Permission updated') }}");
                        updateGroupCheckboxes();
                    }
                }
            });
        }

        $(document).ready(function() {
            // Toggle sections when clicking on headers
            $('.permission-group-header').on('click', function() {
                $(this).toggleClass('collapsed');
                $(this).find('.toggle-icon').toggleClass('fa-chevron-down fa-chevron-up');
                $(this).next('.collapse').collapse('toggle');
            });

            // Select all permissions in a group
            $('.group-select-all').on('change', function() {
                const groupId = $(this).data('group');
                const isChecked = $(this).prop('checked');
                const checkboxes = $(`.permission-checkbox[data-group="${groupId}"]`);

                checkboxes.each(function() {
                    const currentValue = $(this).prop('checked');
                    if (currentValue !== isChecked) {
                        $(this).prop('checked', isChecked);
                        const id = $(this).attr('id').split('-');
                        const user_id = id[0];
                        const permission = id.slice(1).join('-');
                        setPermission(user_id, permission);
                    }
                });
            });

            // Update all group checkboxes
            updateGroupCheckboxes();
        });

        function updateGroupCheckboxes() {
            $('.group-select-all').each(function() {
                const groupId = $(this).data('group');
                const checkboxes = $(`.permission-checkbox[data-group="${groupId}"]`);
                const checkedCheckboxes = $(`.permission-checkbox[data-group="${groupId}"]:checked`);
                $(this).prop('checked', checkboxes.length === checkedCheckboxes.length);
            });
        }
    </script>
@endpush
