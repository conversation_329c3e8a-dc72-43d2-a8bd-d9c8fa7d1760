@extends('admin.navigation')

@section('content')
<div class="ol-card radius-8px">
    <div class="ol-card-body my-3 py-4 px-20px">
        <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
            <h4 class="title fs-16px">
                <i class="fi-rr-settings-sliders me-2"></i>
                Test Editor - So sánh Summernote và TinyMCE
            </h4>
        </div>
    </div>
</div>

<div class="row">
    <!-- Summernote Editor -->
    <div class="col-md-6">
        <div class="ol-card">
            <div class="ol-card-body">
                <h5 class="mb-3">Summernote Editor (Hiện tại - Đã cải thiện)</h5>
                <form>
                    <div class="mb-3">
                        <label class="form-label">Nội dung với Summernote:</label>
                        <textarea name="summernote_content" class="form-control text_editor" rows="10">
                            <h2>Tiêu đề mẫu</h2>
                            <p>Đ<PERSON>y là đoạn văn bản mẫu. <PERSON><PERSON><PERSON> thử copy nội dung từ <strong>Microsoft Word</strong> và paste vào đây để kiểm tra khả năng giữ nguyên định dạng.</p>
                            <ul>
                                <li>Danh sách có thứ tự</li>
                                <li>Với nhiều mục</li>
                                <li>Và định dạng <em>in nghiêng</em></li>
                            </ul>
                            <p style="color: blue;">Văn bản có màu xanh</p>
                        </textarea>
                    </div>
                    <button type="button" class="btn btn-primary" onclick="getSummernoteContent()">Lấy nội dung Summernote</button>
                </form>
            </div>
        </div>
    </div>

    <!-- TinyMCE Editor -->
    <div class="col-md-6">
        <div class="ol-card">
            <div class="ol-card-body">
                <h5 class="mb-3">TinyMCE Editor (Mới - Hiện đại hơn)</h5>
                <form>
                    <div class="mb-3">
                        <label class="form-label">Nội dung với TinyMCE:</label>
                        <textarea name="tinymce_content" class="form-control tinymce-editor" rows="10">
                            <h2>Tiêu đề mẫu</h2>
                            <p>Đây là đoạn văn bản mẫu. Hãy thử copy nội dung từ <strong>Microsoft Word</strong> và paste vào đây để kiểm tra khả năng giữ nguyên định dạng.</p>
                            <ul>
                                <li>Danh sách có thứ tự</li>
                                <li>Với nhiều mục</li>
                                <li>Và định dạng <em>in nghiêng</em></li>
                            </ul>
                            <p style="color: blue;">Văn bản có màu xanh</p>
                        </textarea>
                    </div>
                    <button type="button" class="btn btn-success" onclick="getTinyMCEContent()">Lấy nội dung TinyMCE</button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="ol-card">
            <div class="ol-card-body">
                <h5 class="mb-3">Kết quả so sánh</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Nội dung từ Summernote:</h6>
                        <div id="summernote-result" class="border p-3" style="min-height: 200px; background-color: #f8f9fa;"></div>
                    </div>
                    <div class="col-md-6">
                        <h6>Nội dung từ TinyMCE:</h6>
                        <div id="tinymce-result" class="border p-3" style="min-height: 200px; background-color: #f8f9fa;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="ol-card">
            <div class="ol-card-body">
                <h5 class="mb-3">Hướng dẫn test</h5>
                <div class="alert alert-info">
                    <h6>Để test khả năng paste từ Word:</h6>
                    <ol>
                        <li>Mở Microsoft Word và tạo một document với định dạng phức tạp (màu sắc, font chữ, danh sách, bảng, v.v.)</li>
                        <li>Copy nội dung từ Word (Ctrl+C)</li>
                        <li>Paste vào cả hai editor (Ctrl+V)</li>
                        <li>Click "Lấy nội dung" để xem kết quả</li>
                        <li>So sánh chất lượng giữ nguyên định dạng giữa hai editor</li>
                    </ol>
                </div>
                <div class="alert alert-warning">
                    <strong>Lưu ý:</strong> TinyMCE có khả năng xử lý paste từ Word tốt hơn nhờ plugin PowerPaste chuyên dụng.
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<!-- TinyMCE CSS -->
<style>
.tox-tinymce {
    border-radius: 8px !important;
}
.comparison-result {
    max-height: 300px;
    overflow-y: auto;
}
</style>
@endpush

@push('js')
<!-- TinyMCE CDN -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>

<!-- TinyMCE Configuration -->
<script src="{{ asset('assets/global/tinymce/tinymce-config.js') }}"></script>

<script>
$(document).ready(function() {
    // Initialize TinyMCE with custom configuration
    tinymce.init({
        selector: '.tinymce-editor',
        height: 400,
        menubar: true,
        branding: false,
        
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste'
        ],
        
        toolbar: [
            'undo redo | bold italic underline strikethrough | fontfamily fontsize blocks | alignleft aligncenter alignright alignjustify',
            'outdent indent | numlist bullist | forecolor backcolor removeformat | charmap | fullscreen preview | insertfile image media template link anchor | code'
        ],
        
        // Enhanced paste configuration for Word documents
        paste_data_images: true,
        paste_word_valid_elements: "b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-weight,text-decoration,text-align",
        paste_retain_style_properties: "color font-size font-weight text-decoration text-align",
        paste_remove_styles_if_webkit: false,
        paste_merge_formats: true,
        paste_auto_cleanup_on_paste: true,
        paste_convert_word_fake_lists: true,
        paste_webkit_styles: "color font-size font-weight text-decoration text-align",
        
        content_style: `
            body { 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; 
                font-size: 14px; 
                line-height: 1.6; 
                color: #333;
                margin: 1rem;
            }
        `,
        
        setup: function(editor) {
            editor.ui.registry.addButton('cleanword', {
                text: 'Dọn Word',
                tooltip: 'Dọn dẹp định dạng từ Microsoft Word',
                onAction: function() {
                    var content = editor.getContent();
                    var cleanedContent = content
                        .replace(/<!--[\s\S]*?-->/g, '')
                        .replace(/<o:p\s*\/?>|<\/o:p>/gi, '')
                        .replace(/<w:[^>]*>[\s\S]*?<\/w:[^>]*>/gi, '')
                        .replace(/\s*mso-[^:]+:[^;"]+;?/gi, '')
                        .replace(/\s*MARGIN[^;"]*;?/gi, '')
                        .replace(/\s*style="\s*"/gi, '')
                        .replace(/<span[^>]*>\s*<\/span>/gi, '')
                        .replace(/<p[^>]*>\s*<\/p>/gi, '')
                        .replace(/&nbsp;/g, ' ')
                        .replace(/\s+/g, ' ');
                    
                    editor.setContent(cleanedContent);
                    alert('Đã dọn dẹp định dạng Word thành công!');
                }
            });
        }
    });
});

// Function to get Summernote content
function getSummernoteContent() {
    var content = $('textarea[name="summernote_content"]').summernote('code');
    $('#summernote-result').html('<pre>' + escapeHtml(content) + '</pre>');
}

// Function to get TinyMCE content
function getTinyMCEContent() {
    var content = tinymce.get('tinymce_content') ? tinymce.get('tinymce_content').getContent() : '';
    $('#tinymce-result').html('<pre>' + escapeHtml(content) + '</pre>');
}

// Helper function to escape HTML
function escapeHtml(text) {
    var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}
</script>
@endpush
