/**
 * Enhanced Word Paste Plugin for Summernote
 * Improves handling of Microsoft Word document paste operations
 */

(function (factory) {
    if (typeof define === 'function' && define.amd) {
        define(['jquery'], factory);
    } else if (typeof module === 'object' && module.exports) {
        module.exports = factory(require('jquery'));
    } else {
        factory(window.jQuery);
    }
}(function ($) {
    'use strict';

    // Word Paste Cleaner
    var WordPasteCleaner = {
        
        /**
         * Clean Word HTML content
         * @param {string} html - Raw HTML from Word
         * @returns {string} - Cleaned HTML
         */
        cleanWordHtml: function(html) {
            if (!html) return '';
            
            // Step 1: Remove Word-specific comments and conditional statements
            html = html.replace(/<!--[\s\S]*?-->/g, '');
            html = html.replace(/<!\[if[\s\S]*?\]>/gi, '');
            html = html.replace(/<!\[endif\]>/gi, '');
            
            // Step 2: Remove Word-specific namespaces and tags
            html = html.replace(/<\/?\w+:[^>]*>/gi, ''); // Remove namespace tags like <o:p>, <w:*>
            html = html.replace(/<\?xml[^>]*>/gi, ''); // Remove XML declarations
            
            // Step 3: Clean up MSO styles and attributes
            html = html.replace(/\s*mso-[^:]+:[^;"']*;?/gi, ''); // Remove MSO styles
            html = html.replace(/\s*MARGIN[^;"']*;?/gi, ''); // Remove margin styles
            html = html.replace(/\s*margin[^;"']*;?/gi, ''); // Remove margin styles (lowercase)
            html = html.replace(/\s*tab-stops[^;"']*;?/gi, ''); // Remove tab-stops
            html = html.replace(/\s*text-indent[^;"']*;?/gi, ''); // Remove text-indent
            html = html.replace(/\s*line-height[^;"']*;?/gi, ''); // Remove line-height
            
            // Step 4: Clean font and size attributes (let editor handle these)
            html = html.replace(/\s*font-family[^;"']*;?/gi, '');
            html = html.replace(/\s*font-size[^;"']*;?/gi, '');
            html = html.replace(/\s*FONT-FAMILY[^;"']*;?/gi, '');
            html = html.replace(/\s*FONT-SIZE[^;"']*;?/gi, '');
            
            // Step 5: Remove Word-specific class names
            html = html.replace(/\s*class="[^"]*Mso[^"]*"/gi, '');
            html = html.replace(/\s*class="[^"]*Word[^"]*"/gi, '');
            html = html.replace(/\s*class="[^"]*Normal[^"]*"/gi, '');
            
            // Step 6: Clean up empty attributes
            html = html.replace(/\s*style="\s*"/gi, '');
            html = html.replace(/\s*class="\s*"/gi, '');
            html = html.replace(/\s*lang="[^"]*"/gi, '');
            html = html.replace(/\s*xml:lang="[^"]*"/gi, '');
            
            // Step 7: Remove empty tags
            html = html.replace(/<span[^>]*>\s*<\/span>/gi, '');
            html = html.replace(/<div[^>]*>\s*<\/div>/gi, '');
            html = html.replace(/<p[^>]*>\s*<\/p>/gi, '');
            html = html.replace(/<font[^>]*>\s*<\/font>/gi, '');
            
            // Step 8: Convert Word-specific elements
            html = html.replace(/<p[^>]*class="[^"]*list[^"]*"[^>]*>/gi, '<li>');
            html = html.replace(/<\/p>(\s*<p[^>]*class="[^"]*list[^"]*"[^>]*>)/gi, '</li>$1');
            
            // Step 9: Clean up whitespace and special characters
            html = html.replace(/&nbsp;/g, ' '); // Replace non-breaking spaces
            html = html.replace(/\u00A0/g, ' '); // Replace Unicode non-breaking spaces
            html = html.replace(/\s+/g, ' '); // Normalize whitespace
            html = html.replace(/>\s+</g, '><'); // Remove whitespace between tags
            
            // Step 10: Fix common Word formatting issues
            html = html.replace(/<b\s*>/gi, '<strong>');
            html = html.replace(/<\/b>/gi, '</strong>');
            html = html.replace(/<i\s*>/gi, '<em>');
            html = html.replace(/<\/i>/gi, '</em>');
            
            // Step 11: Remove any remaining empty style attributes
            html = html.replace(/\s*style="[^"]*"/gi, function(match) {
                var styleContent = match.replace(/style="/gi, '').replace(/"/gi, '');
                // Keep only essential styles
                var keepStyles = [];
                if (styleContent.includes('color:')) {
                    var colorMatch = styleContent.match(/color:\s*([^;]+)/i);
                    if (colorMatch) keepStyles.push('color:' + colorMatch[1]);
                }
                if (styleContent.includes('background-color:')) {
                    var bgColorMatch = styleContent.match(/background-color:\s*([^;]+)/i);
                    if (bgColorMatch) keepStyles.push('background-color:' + bgColorMatch[1]);
                }
                if (styleContent.includes('text-align:')) {
                    var alignMatch = styleContent.match(/text-align:\s*([^;]+)/i);
                    if (alignMatch) keepStyles.push('text-align:' + alignMatch[1]);
                }
                
                return keepStyles.length > 0 ? ' style="' + keepStyles.join(';') + '"' : '';
            });
            
            return html.trim();
        },
        
        /**
         * Extract and clean plain text from clipboard
         * @param {string} text - Plain text from clipboard
         * @returns {string} - Cleaned text
         */
        cleanPlainText: function(text) {
            if (!text) return '';
            
            // Remove excessive whitespace
            text = text.replace(/\r\n/g, '\n');
            text = text.replace(/\r/g, '\n');
            text = text.replace(/\n{3,}/g, '\n\n');
            text = text.replace(/[ \t]+/g, ' ');
            
            // Convert to basic HTML
            text = text.replace(/\n\n/g, '</p><p>');
            text = text.replace(/\n/g, '<br>');
            text = '<p>' + text + '</p>';
            
            // Clean up empty paragraphs
            text = text.replace(/<p>\s*<\/p>/g, '');
            text = text.replace(/<p><br><\/p>/g, '');
            
            return text;
        }
    };

    // Enhanced Summernote configuration for Word paste
    $.extend($.summernote.options, {
        wordPaste: {
            enabled: true,
            cleanOnPaste: true,
            showNotification: true
        }
    });

    // Override the default paste callback
    var originalPasteCallback = function(e) {
        var clipboardData = e.originalEvent.clipboardData || window.clipboardData;
        var htmlData = clipboardData.getData('text/html');
        var textData = clipboardData.getData('text/plain');
        
        if (htmlData || textData) {
            e.preventDefault();
            
            var cleanedContent = '';
            var isWordContent = false;
            
            // Check if content is from Word
            if (htmlData && (htmlData.includes('mso-') || htmlData.includes('Microsoft') || htmlData.includes('Word'))) {
                isWordContent = true;
                cleanedContent = WordPasteCleaner.cleanWordHtml(htmlData);
            } else if (htmlData) {
                // Regular HTML content - light cleaning
                cleanedContent = htmlData
                    .replace(/<!--[\s\S]*?-->/g, '')
                    .replace(/<script[\s\S]*?<\/script>/gi, '')
                    .replace(/<style[\s\S]*?<\/style>/gi, '');
            } else if (textData) {
                // Plain text - convert to HTML
                cleanedContent = WordPasteCleaner.cleanPlainText(textData);
            }
            
            if (cleanedContent) {
                $(this).summernote('pasteHTML', cleanedContent);
                
                // Show notification if enabled
                if ($.summernote.options.wordPaste.showNotification && isWordContent) {
                    // Create a simple notification
                    var notification = $('<div class="word-paste-notification">')
                        .css({
                            position: 'fixed',
                            top: '20px',
                            right: '20px',
                            background: '#28a745',
                            color: 'white',
                            padding: '10px 15px',
                            borderRadius: '5px',
                            zIndex: 9999,
                            fontSize: '14px'
                        })
                        .text('Đã dọn dẹp định dạng Word thành công!')
                        .appendTo('body');
                    
                    setTimeout(function() {
                        notification.fadeOut(function() {
                            notification.remove();
                        });
                    }, 3000);
                }
            }
        }
    };

    // Apply enhanced paste handling to all Summernote instances
    $(document).on('summernote.init', function(we, e) {
        var $note = $(e.editable);
        $note.off('paste.wordpaste').on('paste.wordpaste', originalPasteCallback);
    });

    // For existing instances
    $(document).ready(function() {
        $('.note-editable').each(function() {
            $(this).off('paste.wordpaste').on('paste.wordpaste', originalPasteCallback);
        });
    });

    // Export for manual use
    window.SummernoteWordPaste = WordPasteCleaner;
    
}));
