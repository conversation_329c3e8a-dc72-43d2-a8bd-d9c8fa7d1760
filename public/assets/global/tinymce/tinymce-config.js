/**
 * TinyMCE Configuration for Enhanced Word Paste Support
 * Modern WYSIWYG editor with excellent Word document compatibility
 */

// TinyMCE Configuration Object
window.TinyMCEConfig = {
    // Basic configuration
    selector: '.tinymce-editor',
    height: 400,
    menubar: true,
    branding: false,
    
    // Plugins for enhanced functionality
    plugins: [
        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
        'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste',
        'powerpaste', 'autosave', 'save', 'directionality', 'emoticons',
        'template', 'codesample', 'hr', 'pagebreak', 'nonbreaking',
        'toc', 'imagetools', 'textpattern', 'noneditable', 'quickbars'
    ],
    
    // Toolbar configuration
    toolbar: [
        'undo redo | bold italic underline strikethrough | fontfamily fontsize blocks | alignleft aligncenter alignright alignjustify',
        'outdent indent | numlist bullist | forecolor backcolor removeformat | pagebreak | charmap emoticons | fullscreen preview save print | insertfile image media template link anchor codesample | ltr rtl'
    ],
    
    // Content styling
    content_style: `
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; 
            font-size: 14px; 
            line-height: 1.6; 
            color: #333;
            margin: 1rem;
        }
        h1, h2, h3, h4, h5, h6 { margin-top: 1rem; margin-bottom: 0.5rem; }
        p { margin-bottom: 1rem; }
        img { max-width: 100%; height: auto; }
        table { border-collapse: collapse; width: 100%; }
        table td, table th { border: 1px solid #ddd; padding: 8px; }
        table th { background-color: #f2f2f2; }
        blockquote { 
            border-left: 4px solid #ccc; 
            margin: 1rem 0; 
            padding-left: 1rem; 
            color: #666; 
        }
        code { 
            background-color: #f4f4f4; 
            padding: 2px 4px; 
            border-radius: 3px; 
            font-family: 'Courier New', monospace; 
        }
        pre { 
            background-color: #f4f4f4; 
            padding: 1rem; 
            border-radius: 5px; 
            overflow-x: auto; 
        }
    `,
    
    // Enhanced paste configuration for Word documents
    paste_data_images: true,
    paste_word_valid_elements: "b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-weight,text-decoration,text-align",
    paste_retain_style_properties: "color font-size font-weight text-decoration text-align",
    paste_remove_styles_if_webkit: false,
    paste_merge_formats: true,
    paste_auto_cleanup_on_paste: true,
    paste_convert_word_fake_lists: true,
    paste_webkit_styles: "color font-size font-weight text-decoration text-align",
    paste_remove_spans: false,
    paste_remove_styles: false,
    
    // PowerPaste plugin configuration (for better Word support)
    powerpaste_word_import: 'clean',
    powerpaste_html_import: 'clean',
    powerpaste_allow_local_images: true,
    powerpaste_clean_filtered_inline_elements: ['span'],
    
    // Image handling
    images_upload_url: '/admin/upload-image',
    images_upload_credentials: true,
    images_upload_handler: function (blobInfo, success, failure) {
        var xhr, formData;
        xhr = new XMLHttpRequest();
        xhr.withCredentials = false;
        xhr.open('POST', '/admin/upload-image');
        
        // Add CSRF token
        var token = document.querySelector('meta[name="csrf-token"]');
        if (token) {
            xhr.setRequestHeader('X-CSRF-TOKEN', token.getAttribute('content'));
        }
        
        xhr.onload = function() {
            var json;
            if (xhr.status != 200) {
                failure('HTTP Error: ' + xhr.status);
                return;
            }
            json = JSON.parse(xhr.responseText);
            if (!json || typeof json.location != 'string') {
                failure('Invalid JSON: ' + xhr.responseText);
                return;
            }
            success(json.location);
        };
        
        formData = new FormData();
        formData.append('file', blobInfo.blob(), blobInfo.filename());
        xhr.send(formData);
    },
    
    // File picker for links and images
    file_picker_types: 'image',
    file_picker_callback: function(callback, value, meta) {
        if (meta.filetype === 'image') {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = this.files[0];
                var reader = new FileReader();
                reader.onload = function () {
                    var id = 'blobid' + (new Date()).getTime();
                    var blobCache = tinymce.activeEditor.editorUpload.blobCache;
                    var base64 = reader.result.split(',')[1];
                    var blobInfo = blobCache.create(id, file, base64);
                    blobCache.add(blobInfo);
                    callback(blobInfo.blobUri(), { title: file.name });
                };
                reader.readAsDataURL(file);
            };
            input.click();
        }
    },
    
    // Additional configuration
    browser_spellcheck: true,
    contextmenu: "link image table",
    quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
    quickbars_insert_toolbar: 'quickimage quicktable',
    
    // Auto-save configuration
    autosave_ask_before_unload: true,
    autosave_interval: '30s',
    autosave_prefix: '{path}{query}-{id}-',
    autosave_restore_when_empty: false,
    autosave_retention: '2m',
    
    // Template configuration
    templates: [
        {
            title: 'Bài viết cơ bản',
            description: 'Template cơ bản cho bài viết',
            content: '<h2>Tiêu đề</h2><p>Nội dung bài viết...</p>'
        },
        {
            title: 'Bài học',
            description: 'Template cho bài học',
            content: '<h1>Tên bài học</h1><h2>Mục tiêu</h2><p>Mô tả mục tiêu bài học...</p><h2>Nội dung</h2><p>Nội dung chi tiết...</p><h2>Bài tập</h2><p>Các bài tập thực hành...</p>'
        }
    ],
    
    // Language
    language: 'vi',
    
    // Setup callback
    setup: function(editor) {
        // Custom button for cleaning Word formatting
        editor.ui.registry.addButton('cleanword', {
            text: 'Dọn dẹp Word',
            tooltip: 'Dọn dẹp định dạng từ Microsoft Word',
            onAction: function() {
                var content = editor.getContent();
                var cleanedContent = content
                    .replace(/<!--[\s\S]*?-->/g, '')
                    .replace(/<o:p\s*\/?>|<\/o:p>/gi, '')
                    .replace(/<w:[^>]*>[\s\S]*?<\/w:[^>]*>/gi, '')
                    .replace(/\s*mso-[^:]+:[^;"]+;?/gi, '')
                    .replace(/\s*MARGIN[^;"]*;?/gi, '')
                    .replace(/\s*style="\s*"/gi, '')
                    .replace(/<span[^>]*>\s*<\/span>/gi, '')
                    .replace(/<p[^>]*>\s*<\/p>/gi, '')
                    .replace(/&nbsp;/g, ' ')
                    .replace(/\s+/g, ' ');
                
                editor.setContent(cleanedContent);
                editor.notificationManager.open({
                    text: 'Đã dọn dẹp định dạng Word thành công!',
                    type: 'success',
                    timeout: 3000
                });
            }
        });
        
        // Add the custom button to toolbar
        editor.on('init', function() {
            editor.ui.registry.addButton('cleanword', {
                text: 'Dọn Word',
                tooltip: 'Dọn dẹp định dạng từ Microsoft Word',
                onAction: function() {
                    var content = editor.getContent();
                    var cleanedContent = content
                        .replace(/<!--[\s\S]*?-->/g, '')
                        .replace(/<o:p\s*\/?>|<\/o:p>/gi, '')
                        .replace(/<w:[^>]*>[\s\S]*?<\/w:[^>]*>/gi, '')
                        .replace(/\s*mso-[^:]+:[^;"]+;?/gi, '')
                        .replace(/\s*MARGIN[^;"]*;?/gi, '')
                        .replace(/\s*style="\s*"/gi, '')
                        .replace(/<span[^>]*>\s*<\/span>/gi, '')
                        .replace(/<p[^>]*>\s*<\/p>/gi, '')
                        .replace(/&nbsp;/g, ' ')
                        .replace(/\s+/g, ' ');
                    
                    editor.setContent(cleanedContent);
                    editor.notificationManager.open({
                        text: 'Đã dọn dẹp định dạng Word thành công!',
                        type: 'success',
                        timeout: 3000
                    });
                }
            });
        });
    }
};

// Initialize TinyMCE function
window.initTinyMCE = function(selector, customConfig) {
    var config = Object.assign({}, window.TinyMCEConfig);
    if (selector) {
        config.selector = selector;
    }
    if (customConfig) {
        config = Object.assign(config, customConfig);
    }
    
    return tinymce.init(config);
};

// Auto-initialize on document ready
document.addEventListener('DOMContentLoaded', function() {
    if (typeof tinymce !== 'undefined' && document.querySelector('.tinymce-editor')) {
        window.initTinyMCE();
    }
});
