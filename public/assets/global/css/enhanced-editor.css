/**
 * Enhanced Editor Styles
 * Improvements for both Summernote and TinyMCE editors
 */

/* Summernote Enhancements */
.note-editor {
    border-radius: 8px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e0e0e0 !important;
}

.note-toolbar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-bottom: 1px solid #dee2e6 !important;
    border-radius: 8px 8px 0 0 !important;
    padding: 10px !important;
}

.note-btn-group {
    margin-right: 8px !important;
}

.note-btn {
    border-radius: 4px !important;
    margin: 1px !important;
    transition: all 0.2s ease !important;
}

.note-btn:hover {
    background-color: #007bff !important;
    color: white !important;
    transform: translateY(-1px) !important;
}

.note-btn.active {
    background-color: #0056b3 !important;
    color: white !important;
}

.note-editable {
    padding: 20px !important;
    line-height: 1.6 !important;
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif !important;
    font-size: 14px !important;
    min-height: 200px !important;
}

.note-editable:focus {
    outline: none !important;
    box-shadow: inset 0 0 5px rgba(0, 123, 255, 0.2) !important;
}

.note-statusbar {
    background: #f8f9fa !important;
    border-top: 1px solid #dee2e6 !important;
    border-radius: 0 0 8px 8px !important;
    padding: 8px 15px !important;
    font-size: 12px !important;
    color: #6c757d !important;
}

/* TinyMCE Enhancements */
.tox-tinymce {
    border-radius: 8px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e0e0e0 !important;
}

.tox-toolbar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 8px !important;
}

.tox-tbtn {
    border-radius: 4px !important;
    margin: 1px !important;
    transition: all 0.2s ease !important;
}

.tox-tbtn:hover {
    background-color: #007bff !important;
    color: white !important;
}

.tox-tbtn--enabled {
    background-color: #0056b3 !important;
    color: white !important;
}

.tox-edit-area {
    border-radius: 0 0 8px 8px !important;
}

.tox-edit-area iframe {
    border-radius: 0 0 8px 8px !important;
}

/* Word Paste Notification */
.word-paste-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    z-index: 9999;
    font-size: 14px;
    font-weight: 500;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced Dropdown Menus */
.note-dropdown-menu {
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #e0e0e0 !important;
    padding: 8px 0 !important;
}

.note-dropdown-item {
    padding: 8px 16px !important;
    transition: background-color 0.2s ease !important;
}

.note-dropdown-item:hover {
    background-color: #f8f9fa !important;
}

/* Color Palette Enhancements */
.note-color-palette {
    padding: 10px !important;
}

.note-color-btn {
    width: 20px !important;
    height: 20px !important;
    border-radius: 4px !important;
    margin: 2px !important;
    border: 1px solid #dee2e6 !important;
    transition: transform 0.2s ease !important;
}

.note-color-btn:hover {
    transform: scale(1.1) !important;
    border-color: #007bff !important;
}

/* Table Enhancements */
.note-table {
    border-collapse: collapse !important;
    width: 100% !important;
    margin: 15px 0 !important;
}

.note-table td,
.note-table th {
    border: 1px solid #dee2e6 !important;
    padding: 12px !important;
    text-align: left !important;
}

.note-table th {
    background-color: #f8f9fa !important;
    font-weight: 600 !important;
}

.note-table tr:nth-child(even) {
    background-color: #f8f9fa !important;
}

.note-table tr:hover {
    background-color: #e9ecef !important;
}

/* Link Dialog Enhancements */
.note-link-dialog .form-group {
    margin-bottom: 15px !important;
}

.note-link-dialog .form-control {
    border-radius: 6px !important;
    border: 1px solid #ced4da !important;
    padding: 10px 12px !important;
    transition: border-color 0.2s ease !important;
}

.note-link-dialog .form-control:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* Image Dialog Enhancements */
.note-image-dialog .form-group {
    margin-bottom: 15px !important;
}

.note-image-input {
    border-radius: 6px !important;
    border: 2px dashed #ced4da !important;
    padding: 20px !important;
    text-align: center !important;
    transition: border-color 0.2s ease !important;
}

.note-image-input:hover {
    border-color: #007bff !important;
    background-color: #f8f9fa !important;
}

/* Fullscreen Mode Enhancements */
.note-editor.fullscreen {
    z-index: 9999 !important;
}

.note-editor.fullscreen .note-editable {
    padding: 40px !important;
    max-width: 800px !important;
    margin: 0 auto !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .note-toolbar {
        padding: 5px !important;
    }
    
    .note-btn-group {
        margin-right: 4px !important;
    }
    
    .note-btn {
        padding: 6px 8px !important;
        font-size: 12px !important;
    }
    
    .note-editable {
        padding: 15px !important;
        font-size: 16px !important; /* Prevent zoom on iOS */
    }
    
    .tox-toolbar {
        padding: 4px !important;
    }
    
    .tox-tbtn {
        padding: 4px 6px !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .note-editor {
        background-color: #2d3748 !important;
        border-color: #4a5568 !important;
    }
    
    .note-toolbar {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
        border-bottom-color: #4a5568 !important;
    }
    
    .note-btn {
        color: #e2e8f0 !important;
    }
    
    .note-btn:hover {
        background-color: #4299e1 !important;
    }
    
    .note-editable {
        background-color: #1a202c !important;
        color: #e2e8f0 !important;
    }
    
    .note-statusbar {
        background-color: #2d3748 !important;
        border-top-color: #4a5568 !important;
        color: #a0aec0 !important;
    }
}

/* Loading State */
.editor-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.editor-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error States */
.editor-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.editor-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}
