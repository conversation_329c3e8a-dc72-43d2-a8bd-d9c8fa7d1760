/**
 * Font Fix Script for Summernote Editors
 * Forces refresh of existing editors with improved font handling
 */

(function($) {
    'use strict';

    // Function to fix font issues in existing content
    function fixFontInContent(content) {
        if (!content) return content;
        
        // Normalize common Word fonts to web-safe alternatives
        content = content.replace(/font-family:\s*[^;"']*Calibri[^;"']*/gi, 'font-family: Arial, sans-serif');
        content = content.replace(/font-family:\s*[^;"']*Times New Roman[^;"']*/gi, 'font-family: "Times New Roman", serif');
        content = content.replace(/font-family:\s*[^;"']*Arial[^;"']*/gi, 'font-family: Arial, sans-serif');
        content = content.replace(/font-family:\s*[^;"']*Verdana[^;"']*/gi, 'font-family: Verdana, sans-serif');
        content = content.replace(/font-family:\s*[^;"']*<PERSON>homa[^;"']*/gi, 'font-family: Tahoma, sans-serif');
        content = content.replace(/font-family:\s*[^;"']*Georgia[^;"']*/gi, 'font-family: Georgia, serif');
        
        // Convert pt to px for font sizes
        content = content.replace(/font-size:\s*(\d+)pt/gi, function(match, size) {
            var pxSize = Math.round(parseInt(size) * 1.33);
            if (pxSize < 10) pxSize = 12;
            if (pxSize > 24) pxSize = 18;
            return 'font-size: ' + pxSize + 'px';
        });
        
        // Remove excessive spacing
        content = content.replace(/margin[^;"']*:\s*[^;"']*;?/gi, '');
        content = content.replace(/padding[^;"']*:\s*[^;"']*;?/gi, '');
        content = content.replace(/text-indent[^;"']*:\s*[^;"']*;?/gi, '');
        content = content.replace(/line-height[^;"']*:\s*[^;"']*;?/gi, '');
        
        // Clean up empty styles
        content = content.replace(/style="\s*"/gi, '');
        content = content.replace(/style="[^"]*"/gi, function(match) {
            var styleContent = match.replace(/style="/gi, '').replace(/"/gi, '');
            var keepStyles = [];
            
            // Keep essential styles only
            if (styleContent.includes('color:')) {
                var colorMatch = styleContent.match(/color:\s*([^;]+)/i);
                if (colorMatch && colorMatch[1].trim() !== 'black' && colorMatch[1].trim() !== '#000000') {
                    keepStyles.push('color:' + colorMatch[1].trim());
                }
            }
            
            if (styleContent.includes('background-color:')) {
                var bgColorMatch = styleContent.match(/background-color:\s*([^;]+)/i);
                if (bgColorMatch && bgColorMatch[1].trim() !== 'white' && bgColorMatch[1].trim() !== '#ffffff') {
                    keepStyles.push('background-color:' + bgColorMatch[1].trim());
                }
            }
            
            if (styleContent.includes('text-align:')) {
                var alignMatch = styleContent.match(/text-align:\s*([^;]+)/i);
                if (alignMatch && alignMatch[1].trim() !== 'left') {
                    keepStyles.push('text-align:' + alignMatch[1].trim());
                }
            }
            
            if (styleContent.includes('font-family:')) {
                var fontMatch = styleContent.match(/font-family:\s*([^;]+)/i);
                if (fontMatch) {
                    var fontFamily = fontMatch[1].trim();
                    if (fontFamily.includes('Arial') || fontFamily.includes('Times') || 
                        fontFamily.includes('Verdana') || fontFamily.includes('Georgia') || 
                        fontFamily.includes('Tahoma') || fontFamily.includes('sans-serif') || 
                        fontFamily.includes('serif')) {
                        keepStyles.push('font-family:' + fontFamily);
                    }
                }
            }
            
            if (styleContent.includes('font-size:')) {
                var sizeMatch = styleContent.match(/font-size:\s*([^;]+)/i);
                if (sizeMatch) {
                    var fontSize = sizeMatch[1].trim();
                    if (fontSize.includes('px') && parseInt(fontSize) >= 10 && parseInt(fontSize) <= 24) {
                        keepStyles.push('font-size:' + fontSize);
                    }
                }
            }
            
            return keepStyles.length > 0 ? ' style="' + keepStyles.join('; ') + '"' : '';
        });
        
        return content;
    }

    // Function to refresh all Summernote editors
    function refreshAllEditors() {
        $('.note-editor').each(function() {
            var $editor = $(this);
            var $textarea = $editor.next('textarea');
            
            if ($textarea.length && $textarea.hasClass('text_editor')) {
                try {
                    var currentContent = $textarea.summernote('code');
                    var fixedContent = fixFontInContent(currentContent);
                    
                    if (fixedContent !== currentContent) {
                        $textarea.summernote('code', fixedContent);
                        console.log('Fixed font issues in editor:', $textarea.attr('id') || $textarea.attr('name'));
                    }
                } catch (e) {
                    console.log('Could not fix editor:', e);
                }
            }
        });
    }

    // Function to add font fix button to toolbar
    function addFontFixButton() {
        $('.note-toolbar').each(function() {
            var $toolbar = $(this);
            
            if (!$toolbar.find('.font-fix-btn').length) {
                var $fontFixBtn = $('<div class="note-btn-group">' +
                    '<button type="button" class="note-btn font-fix-btn" title="Sửa font chữ" style="background: #28a745; color: white;">' +
                    '<i class="fa fa-font" style="margin-right: 5px;"></i>Sửa Font' +
                    '</button>' +
                    '</div>');
                
                $fontFixBtn.find('.font-fix-btn').on('click', function(e) {
                    e.preventDefault();
                    var $editor = $(this).closest('.note-editor').next('textarea');
                    
                    if ($editor.length) {
                        var currentContent = $editor.summernote('code');
                        var fixedContent = fixFontInContent(currentContent);
                        $editor.summernote('code', fixedContent);
                        
                        // Show notification
                        var notification = $('<div class="font-fix-notification">')
                            .css({
                                position: 'fixed',
                                top: '20px',
                                right: '20px',
                                background: '#28a745',
                                color: 'white',
                                padding: '12px 20px',
                                borderRadius: '8px',
                                zIndex: 9999,
                                fontSize: '14px',
                                fontWeight: '500'
                            })
                            .text('Đã sửa font chữ thành công!')
                            .appendTo('body');
                        
                        setTimeout(function() {
                            notification.fadeOut(function() {
                                notification.remove();
                            });
                        }, 3000);
                    }
                });
                
                $toolbar.append($fontFixBtn);
            }
        });
    }

    // Auto-run functions
    $(document).ready(function() {
        setTimeout(function() {
            addFontFixButton();
            
            // Auto-fix on page load if needed
            if (window.location.search.includes('autofix=true')) {
                refreshAllEditors();
            }
        }, 1000);
    });

    // Watch for new editors
    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) {
                        var $node = $(node);
                        if ($node.find('.note-editor').length > 0) {
                            setTimeout(addFontFixButton, 500);
                        }
                    }
                });
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Expose functions globally
    window.FontFix = {
        fixFontInContent: fixFontInContent,
        refreshAllEditors: refreshAllEditors,
        addFontFixButton: addFontFixButton
    };

})(jQuery);
