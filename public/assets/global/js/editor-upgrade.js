/**
 * Editor Upgrade Script
 * Automatically upgrades existing Summernote instances with enhanced Word paste support
 */

(function($) {
    'use strict';

    // Enhanced Summernote configuration
    var enhancedSummernoteConfig = {
        height: 180,
        minHeight: null,
        maxHeight: null,
        focus: false,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', 'clear']],
            ['fontname', ['fontname']],
            ['fontsize', ['fontsize']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['height', ['height']],
            ['table', ['table']],
            ['insert', ['link', 'picture', 'video', 'hr']],
            ['view', ['fullscreen', 'codeview']]
        ],
        styleTags: [
            'p', 'blockquote', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
        ],
        fontNames: [
            'Arial', 'Arial Black', 'Comic Sans MS', 'Courier New',
            'Helvetica Neue', 'Helvetica', 'Impact', 'Lucida Grande',
            'Tahoma', 'Times New Roman', 'Verdana', 'Roboto', 'Open Sans',
            'Segoe UI', 'Calibri', 'Cambria', 'Georgia'
        ],
        fontSizes: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '24', '28', '32', '36', '48', '72'],
        callbacks: {
            onPaste: function (e) {
                var clipboardData = e.originalEvent.clipboardData || window.clipboardData;
                var pastedData = clipboardData.getData('text/html') || clipboardData.getData('text/plain');
                
                if (pastedData) {
                    e.preventDefault();
                    
                    // Clean up Word-specific formatting while preserving structure
                    var cleanedData = pastedData
                        // Remove Word-specific namespaces and attributes
                        .replace(/<!--[\s\S]*?-->/g, '') // Remove comments
                        .replace(/<o:p\s*\/?>|<\/o:p>/gi, '') // Remove Word paragraph tags
                        .replace(/<w:[^>]*>[\s\S]*?<\/w:[^>]*>/gi, '') // Remove Word-specific tags
                        .replace(/\s*mso-[^:]+:[^;"]+;?/gi, '') // Remove MSO styles
                        .replace(/\s*MARGIN[^;"]*;?/gi, '') // Remove margin styles
                        .replace(/\s*FONT-FAMILY[^;"]*;?/gi, '') // Remove font-family (will use editor defaults)
                        .replace(/\s*font-family[^;"]*;?/gi, '')
                        .replace(/\s*font-size[^;"]*;?/gi, '') // Remove font-size (will use editor defaults)
                        .replace(/\s*line-height[^;"]*;?/gi, '') // Remove line-height
                        .replace(/\s*text-indent[^;"]*;?/gi, '') // Remove text-indent
                        .replace(/\s*tab-stops[^;"]*;?/gi, '') // Remove tab-stops
                        .replace(/\s*style="\s*"/gi, '') // Remove empty style attributes
                        .replace(/\s*class="[^"]*"/gi, '') // Remove class attributes
                        .replace(/<span[^>]*>\s*<\/span>/gi, '') // Remove empty spans
                        .replace(/<p[^>]*>\s*<\/p>/gi, '') // Remove empty paragraphs
                        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
                        .replace(/\s+/g, ' ') // Normalize whitespace
                        .trim();
                    
                    // Insert the cleaned content
                    $(this).summernote('pasteHTML', cleanedData);
                    
                    // Show notification
                    showWordPasteNotification();
                }
            }
        },
        disableDragAndDrop: false,
        shortcuts: true,
        tabsize: 2,
        spellCheck: true
    };

    // Function to show Word paste notification
    function showWordPasteNotification() {
        var notification = $('<div class="word-paste-notification">')
            .css({
                position: 'fixed',
                top: '20px',
                right: '20px',
                background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                color: 'white',
                padding: '12px 20px',
                borderRadius: '8px',
                boxShadow: '0 4px 15px rgba(40, 167, 69, 0.3)',
                zIndex: 9999,
                fontSize: '14px',
                fontWeight: '500',
                animation: 'slideInRight 0.3s ease-out'
            })
            .text('Đã dọn dẹp định dạng Word thành công!')
            .appendTo('body');
        
        setTimeout(function() {
            notification.fadeOut(function() {
                notification.remove();
            });
        }, 3000);
    }

    // Function to upgrade existing Summernote instances
    function upgradeExistingSummernote() {
        $('.note-editor').each(function() {
            var $editor = $(this);
            var $textarea = $editor.next('textarea');
            
            if ($textarea.length && $textarea.hasClass('text_editor')) {
                var currentContent = $textarea.summernote('code');
                
                // Destroy current instance
                $textarea.summernote('destroy');
                
                // Reinitialize with enhanced config
                $textarea.summernote(enhancedSummernoteConfig);
                
                // Restore content
                $textarea.summernote('code', currentContent);
                
                console.log('Upgraded Summernote instance:', $textarea.attr('id') || $textarea.attr('name'));
            }
        });
    }

    // Function to initialize enhanced Summernote for new elements
    function initializeEnhancedSummernote() {
        $('.text_editor:not(.enhanced)').each(function() {
            var $this = $(this);
            
            // Check if already initialized
            if ($this.next('.note-editor').length === 0) {
                $this.summernote(enhancedSummernoteConfig);
            }
            
            $this.addClass('enhanced');
        });
    }

    // Function to convert Summernote to TinyMCE
    function convertToTinyMCE(selector) {
        if (typeof tinymce === 'undefined') {
            console.error('TinyMCE is not loaded. Please include TinyMCE script first.');
            return;
        }

        $(selector).each(function() {
            var $textarea = $(this);
            var content = '';
            
            // Get content from Summernote if initialized
            if ($textarea.next('.note-editor').length > 0) {
                content = $textarea.summernote('code');
                $textarea.summernote('destroy');
            } else {
                content = $textarea.val();
            }
            
            // Remove text_editor class and add tinymce-editor class
            $textarea.removeClass('text_editor').addClass('tinymce-editor');
            
            // Set content
            $textarea.val(content);
        });

        // Initialize TinyMCE
        if (window.initTinyMCE) {
            window.initTinyMCE(selector);
        } else {
            console.error('TinyMCE config not found. Please include tinymce-config.js');
        }
    }

    // Auto-upgrade on document ready
    $(document).ready(function() {
        // Wait a bit for other scripts to load
        setTimeout(function() {
            upgradeExistingSummernote();
            initializeEnhancedSummernote();
        }, 500);
    });

    // Watch for dynamically added elements
    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        var $node = $(node);
                        
                        // Check if the added node contains text editors
                        var $textEditors = $node.find('.text_editor:not(.enhanced)');
                        if ($textEditors.length > 0) {
                            setTimeout(function() {
                                initializeEnhancedSummernote();
                            }, 100);
                        }
                    }
                });
            }
        });
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Expose functions globally
    window.EditorUpgrade = {
        upgradeExistingSummernote: upgradeExistingSummernote,
        initializeEnhancedSummernote: initializeEnhancedSummernote,
        convertToTinyMCE: convertToTinyMCE,
        enhancedSummernoteConfig: enhancedSummernoteConfig
    };

    // Add CSS for animations
    if (!document.getElementById('editor-upgrade-styles')) {
        var style = document.createElement('style');
        style.id = 'editor-upgrade-styles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            .word-paste-notification {
                animation: slideInRight 0.3s ease-out;
            }
        `;
        document.head.appendChild(style);
    }

})(jQuery);
