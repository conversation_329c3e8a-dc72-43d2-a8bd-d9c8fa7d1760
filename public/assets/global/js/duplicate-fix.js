/**
 * Duplicate Fix Script
 * Prevents duplicate content when pasting in Summernote editors
 */

(function($) {
    'use strict';

    // Function to clear all processing flags
    function clearAllProcessingFlags() {
        $('.note-editable').each(function() {
            $(this).removeData('paste-processing')
                   .removeData('word-paste-processing')
                   .removeData('processing');
        });
        
        $('.text_editor').each(function() {
            $(this).removeData('paste-processing')
                   .removeData('word-paste-processing')
                   .removeData('processing');
        });
        
        console.log('Cleared all processing flags');
    }

    // Function to prevent duplicate paste events
    function preventDuplicatePaste() {
        // Remove existing paste event handlers
        $('.note-editable').off('paste.duplicate-fix');
        
        // Add new controlled paste handler
        $('.note-editable').on('paste.duplicate-fix', function(e) {
            var $this = $(this);
            
            // Check if already processing
            if ($this.data('paste-active')) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
            
            // Set processing flag
            $this.data('paste-active', true);
            
            // Clear flag after a short delay
            setTimeout(function() {
                $this.removeData('paste-active');
            }, 500);
        });
    }

    // Function to fix duplicate content in editor
    function fixDuplicateContent() {
        $('.note-editable').each(function() {
            var $editable = $(this);
            var content = $editable.html();
            
            if (content) {
                // Remove duplicate consecutive paragraphs
                var lines = content.split('\n');
                var uniqueLines = [];
                var lastLine = '';
                
                for (var i = 0; i < lines.length; i++) {
                    var currentLine = lines[i].trim();
                    if (currentLine !== lastLine || currentLine === '') {
                        uniqueLines.push(lines[i]);
                        lastLine = currentLine;
                    }
                }
                
                var cleanedContent = uniqueLines.join('\n');
                
                // Remove duplicate HTML blocks
                cleanedContent = cleanedContent.replace(/(<p[^>]*>.*?<\/p>)\s*\1/gi, '$1');
                cleanedContent = cleanedContent.replace(/(<div[^>]*>.*?<\/div>)\s*\1/gi, '$1');
                cleanedContent = cleanedContent.replace(/(<h[1-6][^>]*>.*?<\/h[1-6]>)\s*\1/gi, '$1');
                cleanedContent = cleanedContent.replace(/(<li[^>]*>.*?<\/li>)\s*\1/gi, '$1');
                
                // Remove excessive line breaks
                cleanedContent = cleanedContent.replace(/(<br\s*\/?>){3,}/gi, '<br><br>');
                cleanedContent = cleanedContent.replace(/(<\/p>\s*){2,}/gi, '</p>');
                
                if (cleanedContent !== content) {
                    var $textarea = $editable.closest('.note-editor').next('textarea');
                    if ($textarea.length) {
                        $textarea.summernote('code', cleanedContent);
                        console.log('Fixed duplicate content in editor');
                    }
                }
            }
        });
    }

    // Function to reset all editors
    function resetAllEditors() {
        clearAllProcessingFlags();
        preventDuplicatePaste();
        
        // Remove any stuck processing states
        $('.note-editor').removeClass('processing');
        $('.text_editor').removeClass('processing');
        
        console.log('Reset all editors');
    }

    // Auto-run on document ready
    $(document).ready(function() {
        setTimeout(function() {
            resetAllEditors();
        }, 1000);
    });

    // Run every 30 seconds to prevent stuck states
    setInterval(function() {
        clearAllProcessingFlags();
    }, 30000);

    // Add manual fix button to page
    function addFixButton() {
        if (!$('#duplicate-fix-btn').length) {
            var $fixBtn = $('<button id="duplicate-fix-btn" type="button" style="position: fixed; top: 10px; right: 10px; z-index: 9999; background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 4px; font-size: 12px; cursor: pointer;">Fix Duplicate</button>');
            
            $fixBtn.on('click', function() {
                fixDuplicateContent();
                resetAllEditors();
                
                $(this).text('Fixed!').css('background', '#28a745');
                setTimeout(function() {
                    $fixBtn.text('Fix Duplicate').css('background', '#dc3545');
                }, 2000);
            });
            
            $('body').append($fixBtn);
        }
    }

    // Add fix button in development mode
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        addFixButton();
    }

    // Expose functions globally
    window.DuplicateFix = {
        clearAllProcessingFlags: clearAllProcessingFlags,
        preventDuplicatePaste: preventDuplicatePaste,
        fixDuplicateContent: fixDuplicateContent,
        resetAllEditors: resetAllEditors
    };

    // Handle page visibility change
    $(document).on('visibilitychange', function() {
        if (!document.hidden) {
            setTimeout(clearAllProcessingFlags, 1000);
        }
    });

    // Handle window focus
    $(window).on('focus', function() {
        setTimeout(clearAllProcessingFlags, 500);
    });

})(jQuery);
